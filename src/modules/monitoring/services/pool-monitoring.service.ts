import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { 
  MonitoringEvent as MonitoringEventInterface,
  PoolUtilizationMetrics as PoolUtilizationMetricsInterface,
  QuestionReuseMetrics as QuestionReuseMetricsInterface,
  GenerationTimeMetrics as GenerationTimeMetricsInterface,
  ValidationMetrics as ValidationMetricsInterface,
  CacheMetrics as CacheMetricsInterface,
  EventEmitter
} from '../interfaces/monitoring-events.interface';
import { MonitoringEvent } from '../schemas/monitoring-event.schema';
import { PoolUtilizationMetrics } from '../schemas/pool-utilization-metrics.schema';
import { QuestionReuseMetrics } from '../schemas/question-reuse-metrics.schema';
import { GenerationTimeMetrics } from '../schemas/generation-time-metrics.schema';
import { ValidationMetrics } from '../schemas/validation-metrics.schema';
import { CacheMetrics } from '../schemas/cache-metrics.schema';

// Forward reference to avoid circular dependency
type QuestionPoolService = import('../../question-pool/question-pool.service').QuestionPoolService;

/**
 * Core monitoring service for question pool analytics
 */
@Injectable()
export class PoolMonitoringService implements EventEmitter {
  private readonly logger = new Logger(PoolMonitoringService.name);

  constructor(
    @InjectModel(MonitoringEvent.name)
    private readonly monitoringEventModel: Model<MonitoringEvent>,
    @InjectModel(PoolUtilizationMetrics.name)
    private readonly poolUtilizationModel: Model<PoolUtilizationMetrics>,
    @InjectModel(QuestionReuseMetrics.name)
    private readonly questionReuseModel: Model<QuestionReuseMetrics>,
    @InjectModel(GenerationTimeMetrics.name)
    private readonly generationTimeModel: Model<GenerationTimeMetrics>,
    @InjectModel(ValidationMetrics.name)
    private readonly validationMetricsModel: Model<ValidationMetrics>,
    @InjectModel(CacheMetrics.name)
    private readonly cacheMetricsModel: Model<CacheMetrics>,
    @Inject(forwardRef(() => 'QuestionPoolService'))
    private readonly questionPoolService: QuestionPoolService,
  ) {
    this.logger.log('Pool Monitoring Service initialized');
  }

  /**
   * Emit a monitoring event
   */
  async emitEvent(event: MonitoringEventInterface): Promise<void> {
    try {
      const eventDoc = new this.monitoringEventModel({
        eventId: event.eventId || uuidv4(),
        type: event.type,
        timestamp: event.timestamp || new Date(),
        userId: event.userId,
        worksheetId: event.worksheetId,
        sessionId: event.sessionId,
        eventData: this.sanitizeEventData(event),
      });

      await eventDoc.save();
      
      this.logger.debug(`Event emitted: ${event.type} - ${event.eventId}`);
    } catch (error) {
      this.logger.error(`Failed to emit event: ${error.message}`, error.stack);
      // Don't throw error to avoid disrupting main application flow
    }
  }

  /**
   * Get events by type and time range
   */
  async getEvents(
    type?: string,
    startDate?: Date,
    endDate?: Date,
    userId?: string,
    worksheetId?: string,
    limit: number = 1000
  ): Promise<MonitoringEvent[]> {
    const query: any = {};

    if (type) query.type = type;
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = startDate;
      if (endDate) query.timestamp.$lte = endDate;
    }
    if (userId) query.userId = userId;
    if (worksheetId) query.worksheetId = worksheetId;

    return this.monitoringEventModel
      .find(query)
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * Calculate pool utilization metrics for a time period
   */
  async calculatePoolUtilization(
    startDate: Date,
    endDate: Date,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<PoolUtilizationMetricsInterface> {
    try {
      // Get question selection events in the time period
      const selectionEvents = await this.monitoringEventModel
        .find({
          type: 'question_selection',
          timestamp: { $gte: startDate, $lte: endDate }
        })
        .exec();

      // Extract unique questions used
      const uniqueQuestionsUsed = new Set<string>();
      const subjectUsage: Record<string, Set<string>> = {};

      selectionEvents.forEach(event => {
        const eventData = event.eventData as any;
        if (eventData.result?.selectedQuestions) {
          eventData.result.selectedQuestions.forEach((q: any) => {
            uniqueQuestionsUsed.add(q.questionId);
            
            if (!subjectUsage[q.subject]) {
              subjectUsage[q.subject] = new Set();
            }
            subjectUsage[q.subject].add(q.questionId);
          });
        }
      });

      // Get actual pool size from QuestionPoolService
      const totalUniqueQuestionsInPool = await this.questionPoolService.getTotalQuestionCount();
      const utilizationRate = totalUniqueQuestionsInPool > 0 ? uniqueQuestionsUsed.size / totalUniqueQuestionsInPool : 0;

      // Get actual question counts by subject
      const subjectCounts = await this.questionPoolService.getQuestionCountsBySubject();

      const subjectBreakdown: Record<string, any> = {};
      Object.entries(subjectUsage).forEach(([subject, questions]) => {
        const totalQuestions = subjectCounts[subject] || 0;
        subjectBreakdown[subject] = {
          totalQuestions,
          usedQuestions: questions.size,
          utilizationRate: totalQuestions > 0 ? questions.size / totalQuestions : 0
        };
      });

      // Add subjects that exist in the pool but weren't used in this time period
      Object.entries(subjectCounts).forEach(([subject, totalQuestions]) => {
        if (!subjectBreakdown[subject]) {
          subjectBreakdown[subject] = {
            totalQuestions,
            usedQuestions: 0,
            utilizationRate: 0
          };
        }
      });

      return {
        timeframe,
        timestamp: new Date(),
        totalUniqueQuestionsInPool,
        uniqueQuestionsUsed: uniqueQuestionsUsed.size,
        utilizationRate,
        subjectBreakdown
      };
    } catch (error) {
      this.logger.error(`Error calculating pool utilization: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate question reuse frequency metrics
   */
  async calculateQuestionReuse(
    startDate: Date,
    endDate: Date,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<QuestionReuseMetricsInterface> {
    try {
      const selectionEvents = await this.monitoringEventModel
        .find({
          type: 'question_selection',
          timestamp: { $gte: startDate, $lte: endDate }
        })
        .exec();

      const questionUsage: Record<string, {
        count: number;
        questionType: string;
        subject: string;
      }> = {};

      selectionEvents.forEach(event => {
        const eventData = event.eventData as any;
        if (eventData.result?.selectedQuestions) {
          eventData.result.selectedQuestions.forEach((q: any) => {
            if (!questionUsage[q.questionId]) {
              questionUsage[q.questionId] = {
                count: 0,
                questionType: q.questionType,
                subject: q.subject
              };
            }
            questionUsage[q.questionId].count++;
          });
        }
      });

      const usageCounts = Object.values(questionUsage).map(q => q.count);
      const averageReuseFrequency = usageCounts.length > 0 
        ? usageCounts.reduce((sum, count) => sum + count, 0) / usageCounts.length 
        : 0;

      const mostReusedQuestions = Object.entries(questionUsage)
        .sort(([, a], [, b]) => b.count - a.count)
        .slice(0, 10)
        .map(([questionId, data]) => ({
          questionId,
          usageCount: data.count,
          questionType: data.questionType,
          subject: data.subject
        }));

      const reuseDistribution: Record<string, number> = {};
      usageCounts.forEach(count => {
        reuseDistribution[count.toString()] = (reuseDistribution[count.toString()] || 0) + 1;
      });

      return {
        timeframe,
        timestamp: new Date(),
        averageReuseFrequency,
        mostReusedQuestions,
        reuseDistribution
      };
    } catch (error) {
      this.logger.error(`Error calculating question reuse: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Sanitize event data for storage
   */
  private sanitizeEventData(event: MonitoringEventInterface): Record<string, any> {
    const { eventId, timestamp, userId, worksheetId, sessionId, type, ...eventData } = event;
    return eventData;
  }

  /**
   * Calculate generation time comparison metrics
   */
  async calculateGenerationTimeComparison(
    startDate: Date,
    endDate: Date,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<GenerationTimeMetricsInterface> {
    try {
      const [selectionEvents, generationEvents] = await Promise.all([
        this.monitoringEventModel.find({
          type: 'question_selection',
          timestamp: { $gte: startDate, $lte: endDate }
        }).exec(),
        this.monitoringEventModel.find({
          type: 'question_generation',
          timestamp: { $gte: startDate, $lte: endDate }
        }).exec()
      ]);

      // Calculate pool selection times
      const poolTimes = selectionEvents
        .map(event => event.eventData?.result?.executionTimeMs)
        .filter(time => time != null);

      // Calculate AI generation times
      const aiTimes = generationEvents
        .map(event => event.eventData?.result?.executionTimeMs)
        .filter(time => time != null);

      const poolStats = this.calculateTimeStats(poolTimes);
      const aiStats = this.calculateTimeStats(aiTimes);

      const comparisonRatio = aiStats.average > 0 ? poolStats.average / aiStats.average : 0;

      return {
        timeframe,
        timestamp: new Date(),
        poolSelectionTime: poolStats,
        aiGenerationTime: aiStats,
        comparisonRatio
      };
    } catch (error) {
      this.logger.error(`Error calculating generation time comparison: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate validation success rate metrics
   */
  async calculateValidationMetrics(
    startDate: Date,
    endDate: Date,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<ValidationMetricsInterface> {
    try {
      const validationEvents = await this.monitoringEventModel
        .find({
          type: 'validation_attempt',
          timestamp: { $gte: startDate, $lte: endDate }
        })
        .exec();

      const totalValidations = validationEvents.length;
      const successfulValidations = validationEvents.filter(
        event => event.eventData?.result?.isValid === true
      ).length;

      const successRate = totalValidations > 0 ? successfulValidations / totalValidations : 0;

      const issueBreakdown: Record<string, { count: number; severity: Record<string, number> }> = {};

      validationEvents.forEach(event => {
        const issues = event.eventData?.result?.issues || [];
        issues.forEach((issue: any) => {
          if (!issueBreakdown[issue.type]) {
            issueBreakdown[issue.type] = { count: 0, severity: {} };
          }
          issueBreakdown[issue.type].count++;
          issueBreakdown[issue.type].severity[issue.severity] =
            (issueBreakdown[issue.type].severity[issue.severity] || 0) + 1;
        });
      });

      return {
        timeframe,
        timestamp: new Date(),
        totalValidations,
        successfulValidations,
        successRate,
        issueBreakdown
      };
    } catch (error) {
      this.logger.error(`Error calculating validation metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate cache performance metrics
   */
  async calculateCacheMetrics(
    startDate: Date,
    endDate: Date,
    cacheType: string,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<CacheMetricsInterface> {
    try {
      const cacheEvents = await this.monitoringEventModel
        .find({
          type: 'cache_interaction',
          'eventData.cacheType': cacheType,
          timestamp: { $gte: startDate, $lte: endDate }
        })
        .exec();

      const totalRequests = cacheEvents.length;
      const hits = cacheEvents.filter(event => event.eventData?.result?.hit === true).length;
      const misses = totalRequests - hits;
      const hitRate = totalRequests > 0 ? hits / totalRequests : 0;

      const responseTimes = cacheEvents
        .map(event => event.eventData?.result?.executionTimeMs)
        .filter(time => time != null);

      const averageResponseTime = responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

      return {
        timeframe,
        timestamp: new Date(),
        cacheType,
        totalRequests,
        hits,
        misses,
        hitRate,
        averageResponseTime
      };
    } catch (error) {
      this.logger.error(`Error calculating cache metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Helper method to calculate time statistics
   */
  private calculateTimeStats(times: number[]) {
    if (times.length === 0) {
      return { average: 0, p50: 0, p95: 0, p99: 0 };
    }

    const sorted = times.sort((a, b) => a - b);
    const average = times.reduce((sum, time) => sum + time, 0) / times.length;

    const p50Index = Math.floor(sorted.length * 0.5);
    const p95Index = Math.floor(sorted.length * 0.95);
    const p99Index = Math.floor(sorted.length * 0.99);

    return {
      average,
      p50: sorted[p50Index] || 0,
      p95: sorted[p95Index] || 0,
      p99: sorted[p99Index] || 0
    };
  }

  /**
   * Get aggregated metrics for dashboard
   */
  async getDashboardMetrics(
    startDate: Date,
    endDate: Date,
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily'
  ) {
    try {
      const [
        poolUtilization,
        questionReuse,
        generationTimeComparison,
        validationMetrics,
        questionPoolCacheMetrics,
        worksheetCacheMetrics
      ] = await Promise.all([
        this.calculatePoolUtilization(startDate, endDate, timeframe),
        this.calculateQuestionReuse(startDate, endDate, timeframe),
        this.calculateGenerationTimeComparison(startDate, endDate, timeframe),
        this.calculateValidationMetrics(startDate, endDate, timeframe),
        this.calculateCacheMetrics(startDate, endDate, 'question_pool', timeframe),
        this.calculateCacheMetrics(startDate, endDate, 'worksheet_document', timeframe)
      ]);

      return {
        poolUtilization,
        questionReuse,
        generationTimeComparison,
        validationMetrics,
        cacheMetrics: {
          questionPool: questionPoolCacheMetrics,
          worksheetDocument: worksheetCacheMetrics
        },
        generatedAt: new Date(),
        timeframe,
        dateRange: { startDate, endDate }
      };
    } catch (error) {
      this.logger.error(`Error getting dashboard metrics: ${error.message}`, error.stack);
      throw error;
    }
  }
}
