import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetGenerateConsumer } from '../queue.consumer';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { AiOrchestrationService } from '../../ai/ai-orchestration.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { WebSocketErrorCode } from '../../../core/enums/websocket-error-codes.enum';
import { SelectionStrategy } from '../dto/worksheet-generation-options.dto';

describe('WorksheetGenerateConsumer - Error Handling Integration', () => {
  let consumer: WorksheetGenerateConsumer;
  let questionPoolService: jest.Mocked<QuestionPoolService>;
  let aiOrchestrationService: jest.Mocked<AiOrchestrationService>;
  let socketGateway: jest.Mocked<SocketGateway>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetGenerateConsumer,
        {
          provide: QuestionPoolService,
          useValue: {
            getRandomQuestions: jest.fn(),
          },
        },
        {
          provide: AiOrchestrationService,
          useValue: {
            generateQuestions: jest.fn(),
          },
        },
        {
          provide: SocketGateway,
          useValue: {
            emitWorksheetProgress: jest.fn(),
            emitDatabaseError: jest.fn(),
            emitAiServiceError: jest.fn(),
            emitQuestionPoolError: jest.fn(),
            emitSystemError: jest.fn(),
            emitRetryAttempt: jest.fn(),
            emitFallbackActivated: jest.fn(),
          },
        },
        // Mock other dependencies
        {
          provide: 'WorksheetService',
          useValue: {},
        },
        {
          provide: 'DocumentsService',
          useValue: {},
        },
        {
          provide: 'PromptService',
          useValue: {},
        },
        {
          provide: 'WorksheetPromptResultModel',
          useValue: {},
        },
        {
          provide: 'BatchImageService',
          useValue: {},
        },
        {
          provide: 'WorksheetDocumentCacheService',
          useValue: {},
        },
        {
          provide: 'QuestionPoolConfigService',
          useValue: {},
        },
        {
          provide: 'ContentValidationService',
          useValue: {},
        },
      ],
    }).compile();

    consumer = module.get<WorksheetGenerateConsumer>(WorksheetGenerateConsumer);
    questionPoolService = module.get(QuestionPoolService);
    aiOrchestrationService = module.get(AiOrchestrationService);
    socketGateway = module.get(SocketGateway);
  });

  describe('MongoDB Connection Retry Logic', () => {
    it('should retry on transient MongoDB errors', async () => {
      const mongoError = new Error('MongoNetworkError: connection failed');
      mongoError.name = 'MongoNetworkError';

      questionPoolService.getRandomQuestions
        .mockRejectedValueOnce(mongoError)
        .mockRejectedValueOnce(mongoError)
        .mockResolvedValueOnce([]);

      const userRequest = {
        subject: 'Mathematics',
        grade: 'Primary 5',
        topic: 'Fractions',
      };

      const result = await (consumer as any).getQuestionsFromPool(userRequest, 10, 'test-worksheet-id');

      expect(questionPoolService.getRandomQuestions).toHaveBeenCalledTimes(3);
      expect(socketGateway.emitRetryAttempt).toHaveBeenCalledTimes(2);
      expect(result).toEqual([]);
    });

    it('should emit database error after max retries', async () => {
      const mongoError = new Error('MongoNetworkError: connection failed');
      mongoError.name = 'MongoNetworkError';

      questionPoolService.getRandomQuestions.mockRejectedValue(mongoError);

      const userRequest = {
        subject: 'Mathematics',
        grade: 'Primary 5',
        topic: 'Fractions',
      };

      const result = await (consumer as any).getQuestionsFromPool(userRequest, 10, 'test-worksheet-id');

      expect(questionPoolService.getRandomQuestions).toHaveBeenCalledTimes(4); // Initial + 3 retries
      expect(socketGateway.emitSystemError).toHaveBeenCalledWith(
        'test-worksheet-id',
        WebSocketErrorCode.MAX_RETRIES_REACHED,
        expect.stringContaining('Question pool query failed after 3 retry attempts'),
        expect.any(Object)
      );
      expect(result).toEqual([]);
    });

    it('should not retry on non-retryable errors', async () => {
      const validationError = new Error('ValidationError: invalid query');
      validationError.name = 'ValidationError';

      questionPoolService.getRandomQuestions.mockRejectedValue(validationError);

      const userRequest = {
        subject: 'Mathematics',
        grade: 'Primary 5',
        topic: 'Fractions',
      };

      const result = await (consumer as any).getQuestionsFromPool(userRequest, 10, 'test-worksheet-id');

      expect(questionPoolService.getRandomQuestions).toHaveBeenCalledTimes(1);
      expect(socketGateway.emitRetryAttempt).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('AI Service Failure Chain', () => {
    it('should handle all AI providers failing', async () => {
      const aiRequest = {
        userRequest: { totalQuestions: 5 },
        documentResults: {},
        worksheetId: 'test-worksheet-id',
        questionCount: 5,
      };

      aiOrchestrationService.generateQuestions.mockResolvedValue({
        questions: [],
        provider: 'none',
        success: false,
        attempts: {
          openai: { success: false, error: 'API key invalid' },
          googleAi: { success: false, error: 'Service unavailable' },
          cachedContent: { success: false, error: 'No cached content available' },
        },
        totalDuration: 5000,
      });

      const result = await (consumer as any).generateQuestionsWithAI({}, aiRequest.userRequest, 'test-worksheet-id');

      expect(socketGateway.emitAiServiceError).toHaveBeenCalledWith(
        'test-worksheet-id',
        WebSocketErrorCode.OPENAI_SERVICE_FAILED,
        'OpenAI service failed to generate questions',
        'OpenAI via OpenRouter',
        expect.any(Object)
      );

      expect(socketGateway.emitAiServiceError).toHaveBeenCalledWith(
        'test-worksheet-id',
        WebSocketErrorCode.GOOGLE_AI_SERVICE_FAILED,
        'Google AI service failed to generate questions',
        'Google AI',
        expect.any(Object)
      );

      expect(socketGateway.emitAiServiceError).toHaveBeenCalledWith(
        'test-worksheet-id',
        WebSocketErrorCode.ALL_AI_SERVICES_FAILED,
        'All AI services and fallbacks failed to generate questions',
        'All Providers',
        expect.any(Object)
      );

      expect(result).toEqual([]);
    });

    it('should succeed when OpenAI works', async () => {
      const mockQuestions = [
        {
          type: 'multiple_choice',
          content: 'What is 2 + 2?',
          options: ['3', '4', '5', '6'],
          answer: '4',
          explain: 'Basic addition',
          imagePrompt: '',
          image: '',
          subject: 'Mathematics',
          parentSubject: 'Mathematics',
          childSubject: 'Addition',
        },
      ];

      aiOrchestrationService.generateQuestions.mockResolvedValue({
        questions: mockQuestions,
        provider: 'openai',
        success: true,
        attempts: {
          openai: { success: true, duration: 2000 },
        },
        totalDuration: 2000,
      });

      const result = await (consumer as any).generateQuestionsWithAI({}, { totalQuestions: 1 }, 'test-worksheet-id');

      expect(socketGateway.emitWorksheetProgress).toHaveBeenCalledWith(
        'test-worksheet-id',
        1,
        1,
        'AI generated 1 questions using openai'
      );

      expect(result).toEqual(mockQuestions);
    });
  });

  describe('Graceful Degradation with AI Fallback', () => {
    it('should activate AI fallback when pool has insufficient questions', async () => {
      // Mock pool returning only 3 out of 10 requested questions
      questionPoolService.getRandomQuestions.mockResolvedValue([
        { type: 'multiple_choice', content: 'Question 1' },
        { type: 'multiple_choice', content: 'Question 2' },
        { type: 'multiple_choice', content: 'Question 3' },
      ] as any);

      // Mock AI generating the remaining 7 questions
      aiOrchestrationService.generateQuestions.mockResolvedValue({
        questions: Array(7).fill({
          type: 'multiple_choice',
          content: 'AI Generated Question',
          options: ['A', 'B', 'C', 'D'],
          answer: 'A',
          explain: 'AI explanation',
          imagePrompt: '',
          image: '',
          subject: 'Mathematics',
          parentSubject: 'Mathematics',
          childSubject: 'Addition',
        }),
        provider: 'openai',
        success: true,
        attempts: { openai: { success: true } },
        totalDuration: 3000,
      });

      const params = {
        worksheetId: 'test-worksheet-id',
        resolvedOptions: { resolvedStrategy: SelectionStrategy.HYBRID },
        documentResults: {},
        userRequest: {
          totalQuestions: 10,
          subject: 'Mathematics',
          grade: 'Primary 5',
          topic: 'Addition',
        },
      };

      const result = await (consumer as any).processHybridQuestionSourcing(params);

      expect(socketGateway.emitFallbackActivated).toHaveBeenCalledWith(
        'test-worksheet-id',
        'question_pool_selection',
        'ai_generation',
        'Insufficient questions in pool: 3/10 available'
      );

      expect(result.result).toHaveLength(10);
    });

    it('should emit error when POOL_ONLY strategy has insufficient questions', async () => {
      questionPoolService.getRandomQuestions.mockResolvedValue([
        { type: 'multiple_choice', content: 'Question 1' },
      ] as any);

      const params = {
        worksheetId: 'test-worksheet-id',
        resolvedOptions: { resolvedStrategy: SelectionStrategy.POOL_ONLY },
        documentResults: {},
        userRequest: {
          totalQuestions: 10,
          subject: 'Mathematics',
          grade: 'Primary 5',
          topic: 'Addition',
        },
      };

      const result = await (consumer as any).processHybridQuestionSourcing(params);

      expect(socketGateway.emitQuestionPoolError).toHaveBeenCalledWith(
        'test-worksheet-id',
        WebSocketErrorCode.INSUFFICIENT_QUESTIONS_NO_FALLBACK,
        expect.stringContaining('Only 1 of 10 requested questions available in pool'),
        expect.objectContaining({
          available: 1,
          requested: 10,
          deficit: 9,
        })
      );

      expect(result.result).toHaveLength(1);
    });
  });
});
