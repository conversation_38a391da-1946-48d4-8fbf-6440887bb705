import { TypeOrmModule } from '@nestjs/typeorm';
import { WorksheetController } from './worksheet.controller';
import { Module, forwardRef } from '@nestjs/common';
import { WorksheetService } from './worksheet.service';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';
import { BullModule } from '@nestjs/bullmq';
import { WorksheetQueueService } from './worksheet-queue.service';
import { WorksheetGenerateConsumer } from './queue.consumer';
import { DocumentsModule } from '../documents/documents.module';
import { PromptModule } from '../prompt/prompt.module';
import { MongodbModule } from '../mongodb/mongodb.module';
import { SocketModule } from '../socket/socket.module';
import { Worksheet } from './entities/worksheet.entity';
import { WorksheetOption } from './entities/worksheet-option.entity';
import { WorksheetPromptResult, WorksheetPromptResultSchema } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { WorksheetDocument, WorksheetDocumentSchema } from '../mongodb/schemas/worksheet-document.schema'; // Import WorksheetDocument and its schema
import { WorksheetCleanupService } from './worksheet-cleanup.service';
import { GenImageModule } from '../gen-image/gen-image.module';
import { WorksheetDocumentCacheService } from './services/worksheet-document-cache.service';
import {QueryCacheModule} from "../mongodb/query-cache.module";
import {QuestionPoolModule} from "../question-pool/question-pool.module";
import { ValidationModule } from '../validation/validation.module';
import { MonitoringModule } from '../monitoring/monitoring.module';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OptionType,
      OptionValue,
      Worksheet,
      WorksheetOption,
    ]),
    MongooseModule.forFeature([
      { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
      { name: WorksheetDocument.name, schema: WorksheetDocumentSchema }, // Add WorksheetDocument to Mongoose features
    ]),
    BullModule.registerQueue({
      name: 'worksheet_generate',
    }),
    DocumentsModule,
    forwardRef(() => PromptModule),
    forwardRef(() => QueryCacheModule),
    MongodbModule,
    SocketModule,
    GenImageModule,
    forwardRef(() => QuestionPoolModule),
    forwardRef(() => MonitoringModule),
    ValidationModule
  ],
  controllers: [WorksheetController],
  providers: [
    WorksheetService,
    WorksheetQueueService,
    WorksheetGenerateConsumer,
    WorksheetCleanupService,
    WorksheetDocumentCacheService,
  ],
  exports: [WorksheetService, WorksheetDocumentCacheService],
})
export class WorksheetModule {}
