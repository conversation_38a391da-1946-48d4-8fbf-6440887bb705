# Task ID: 22
# Title: Implement Monitoring and Analytics System for Question Pool Selection
# Status: done
# Dependencies: 3, 9, 16, 17, 18, 19
# Priority: medium
# Description: Develop a comprehensive monitoring and analytics system to track key performance, quality, and utilization metrics for the random question pool selection process. This includes an admin dashboard for visualizing these metrics.
# Details:
1. **Metric Collection Service:** Create a `MonitoringModule` and `PoolMonitoringService`. Integrate with `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture relevant events and data. Implement mechanisms to log/store metrics, potentially in MongoDB collections or a time-series database.
2. **Key Metrics Implementation:** Track and calculate: 
    a. **Pool Utilization Rate:** Percentage of unique questions used from the pool over time.
    b. **Question Reuse Frequency:** How often individual questions are selected.
    c. **Generation Time Comparison:** Average time for question selection from pool vs. AI generation (from Task 17).
    d. **Validation Success Rate:** Success/failure rate of content validation (from Task 9).
    e. **Distribution Adherence:** How well selected questions meet specified cognitive level/type distributions (from Task 18).
    f. **Query Response Times:** Average/percentile response times for `QuestionPoolService.getRandomQuestions` (from Task 16/19).
    g. **Cache Hit/Miss Ratios:** Track hit/miss ratios for the question pool cache (from Task 19).
3. **Admin Dashboard Development:** Create a new secured section in the admin interface (requires Task 3 for authentication/authorization). Display collected metrics using charts and tables. Dashboard components should include an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.
4. **Data Storage for Metrics:** Define schemas for storing aggregated metrics. Implement data aggregation jobs (e.g., hourly, daily) if raw event logging is used.

# Test Strategy:
1. **Unit Tests:** Verify `PoolMonitoringService` for correct metric calculation logic using mocked dependent services.
2. **Integration Tests:** Ensure events from `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., are correctly captured and stored by the monitoring service. Test data aggregation if implemented.
3. **End-to-End Tests (Admin Dashboard):** 
    a. Log in as an admin user (Task 3).
    b. Navigate to the monitoring dashboard.
    c. Trigger actions that generate metrics (e.g., request worksheets).
    d. Verify the dashboard displays updated and accurate metrics for: pool utilization, question reuse, generation times (pool vs. AI), validation success/failure rates, distribution adherence, query response times, and cache hit/miss ratios.
4. **Performance Tests:** Ensure metric collection does not significantly degrade the performance of core question selection services.

# Subtasks:
## 1. Initialize Monitoring Module and PoolMonitoringService for Event Capture [done]
### Dependencies: None
### Description: Create the `MonitoringModule` and `PoolMonitoringService`. Implement basic event listeners or hooks within `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture raw data points relevant for future metric calculation. Focus on capturing events like question selection, generation attempts, validation results, and cache interactions.
### Details:
Define interfaces for event data (e.g., `QuestionSelectedEvent`, `ValidationAttemptEvent`). Implement initial logging of these raw events, potentially to a structured log stream or a temporary staging area in a database. This subtask focuses on *capturing* data, not comprehensive processing or permanent storage of aggregated metrics yet. Ensure `PoolMonitoringService` has methods to receive these events from the respective services.

## 2. Design Metric Schemas and Implement Data Storage and Aggregation [done]
### Dependencies: 22.1
### Description: Define the database schemas (e.g., MongoDB collections or time-series database structures) for storing both raw event data (if needed for detailed analysis) and aggregated metrics. Implement initial data aggregation jobs (e.g., hourly/daily roll-ups) for key metrics if raw event logging is the primary capture method. Choose an appropriate data store (e.g., MongoDB, Prometheus/InfluxDB).
### Details:
For MongoDB, define collections like `raw_monitoring_events`, `hourly_pool_utilization`, `daily_question_reuse_frequency`, etc. Design schemas considering query patterns for the dashboard. Implement aggregation pipelines or scheduled jobs (e.g., using cron and scripts, or a framework like NestJS Scheduler) to process raw events from Subtask 1 and populate aggregated metric tables/collections.

## 3. Implement Core Metric Calculation Logic: Utilization, Reuse, and Generation Time [done]
### Dependencies: 22.2
### Description: Develop the logic within `PoolMonitoringService` or a dedicated analytics component to calculate: Pool Utilization Rate, Question Reuse Frequency, and Generation Time Comparison (Pool vs. AI). This involves processing the captured events/data (from Subtask 1, stored/aggregated via Subtask 2) and ensuring the calculated metrics are stored according to the defined schemas.
### Details:
Pool Utilization: (Unique questions used from pool / Total unique questions in active pools) over time. Question Reuse: Frequency count for each question ID. Generation Time Comparison: Average time for `QuestionPoolService.getRandomQuestions` (Task 16/19) vs. average time for AI generation (Task 17). Ensure these calculations are integrated into the aggregation jobs or performed by the service, with results stored in the metrics database.

## 4. Implement Supporting Metric Calculation Logic: Validation, Distribution, Query Times, Cache [done]
### Dependencies: 22.3
### Description: Extend the monitoring system to calculate: Validation Success Rate (from `ContentValidationService` events - Task 9), Distribution Adherence (comparing selected questions' metadata against specified cognitive level/type distributions from Task 18), Query Response Times for `QuestionPoolService.getRandomQuestions` (Task 16/19), and Cache Hit/Miss Ratios for `WorksheetDocumentCacheService` (Task 19).
### Details:
Validation Success Rate: (Successful validations / Total validations). Distribution Adherence: Analyze batches of selected questions. Query Response Times: Log and aggregate (avg, p95, p99) response times. Cache Hit/Miss: (Cache hits / Total cache lookups). Integrate these calculations into the existing aggregation/processing flow and store results in the metrics database.

## 5. Develop Admin Dashboard for Visualizing Question Pool Metrics [done]
### Dependencies: 22.4
### Description: Create a new secured section in the admin interface (leveraging Task 3 for authentication/authorization) to display all collected and calculated metrics. Implement UI components including an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.
### Details:
Design API endpoints to serve aggregated metrics data to the frontend. Use a charting library (e.g., Chart.js, Recharts, Nivo) for visualizations. Dashboard should allow filtering by date ranges. Ensure the UI is intuitive and provides actionable insights. Implement role-based access control for this new admin section.

